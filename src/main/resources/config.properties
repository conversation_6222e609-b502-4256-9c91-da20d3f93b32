# project
project.group.id=com.think1024.speedsters
project.plugin.id=com.think1024.speedsters.ideaplugin
project.name=speedsters-auto
project.version=0.0.66

# socket.ioc
socket.io.namespace=/fe
socket.io.path=/ws/socket.io

# locator
locator.file.extension=java
locator.source.path=src/main/java/
locator.class.annotation=com.vs.code.AutoGenerated
locator.method.annotation=com.vs.code.AutoGenerated
locator.default.annotation=AutoGenerated
locator.public.interface.annotation=com.vs.common.util.rpc.pub.PublicInterface
locator.controller.annotation=org.springframework.stereotype.Controller
locator.desktop.url.protocol=tocoapp
locator.desktop.url.protocol.test=tocotest
locator.desktop.url.setproject=setProject?id={0}&name={1}

# login user
url.host.default=https://toco.teitui.com
url.host.test=https://toco-dev1.teitui.com
url.frontend.host.default=https://toco-dev1.teitui.com
url.frontend.host.test=https://local.teitui.com
url.path.login=/w
url.path.logout=/api/user-account/logout
url.path.userinfo=/api/user/info
url.path.search=/api/item/search
url.path.fim=/lm/api/code/completions
url.path.orgs=/mis/api/organization/query-organization-user-info-by-user
url.path.create.project=/mis/api/organization/create-project-to-organization
url.path.create.project.config=/workspace/api/boilerplate/server-project
url.path.create.module.config=/workspace/api/boilerplate/server-module
url.path.create.project.version=/api/vs/version

# toco design app download url
toco.design.app.url=https://toco.teitui.com/manage